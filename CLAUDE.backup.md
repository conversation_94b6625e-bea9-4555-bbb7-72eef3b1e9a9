# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Otlo is a Flutter AI chat application with comprehensive MCP (Model Context Protocol) support and OpenRouter integration. The app provides a sophisticated chat interface with real-time streaming, tool execution capabilities, and multi-platform responsive design.

## User's Project Specific Guidance

### Deprecations

- Never use `.withOpacity(double)` on colors. Use `.withValues(alpha: double)` instead. withOpacity is deprecated in Flutter 

### Workflow Specifications

- Before using any new frameworks always use context7 to pull the latest documentation on the framework.
- Use context7 again and again, religiously, to ensure you have the best possible knowledge on what you are using.
- Do not ignore deprecation warnings when running `flutter analyze`. When you find a deprecation warning, propose a plan to fix it to the user, after you are done finishing your task.

## Essential Development Commands

### Code Generation

```bash
# Generate all code (Drift, Riverpod, Freezed, JSON serialization)
dart run build_runner build --delete-conflicting-outputs
# Clean and rebuild generated code
dart run build_runner clean
```

### Running the Application
```bash
# Run on jay's android (if not detected, run `flutter devices`)
flutter run -d pixel
# if not available, ask user to run it manually.
```

### Testing and Quality
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/path/to/test_file.dart

# Run tests with coverage
flutter test --coverage

# Analyze code
flutter analyze

# Format code
dart format .
```

## High-Level Architecture

### Core Technology Stack
- **State Management**: Riverpod with code generation for type-safe providers
- **Database**: Drift (SQLite) with type-safe DAOs and reactive queries
- **Routing**: GoRouter with nested navigation and adaptive layouts
- **API Integration**: Custom OpenRouter client with streaming support
- **MCP Protocol**: Full JSON-RPC 2.0 implementation for tool execution

### Layer Architecture

**1. Data Layer (`lib/core/`)**
- `database/`: Drift database with tables, DAOs, and migrations
- `api/`: OpenRouter client and base API abstractions
- `mcp/`: Complete MCP protocol implementation (WebSocket/HTTP transports)
- `services/`: Core utilities (token counting, secure storage, export)

**2. State Management (`lib/features/*/providers/`)**
- Riverpod providers with `@riverpod` annotations for code generation
- Reactive database queries through providers
- Settings management with secure storage integration
- Chat state with streaming support and real-time updates

**3. Presentation Layer (`lib/features/`)**
- Feature-based organization: `chat/`, `settings/`, `mcp_management/`
- Each feature contains: `providers/`, `screens/`, `widgets/`, `models/`
- Adaptive UI components for mobile/tablet/desktop layouts

**4. Shared Infrastructure (`lib/shared/`)**
- `theme/`: Complete Material 3 design system
- `widgets/`: Reusable UI components (error views, loading states)
- `providers/`: Cross-cutting providers (database, theme)

### Key Data Flow Patterns

**Chat Message Flow:**
1. User input → `ChatProvider` → OpenRouter API client
2. Streaming response → Real-time UI updates via Riverpod
3. Message persistence → Drift database → Reactive UI refresh

**MCP Tool Execution:**
1. Tool discovery → MCP client → Server connection
2. Tool execution → `ToolRegistry` → Result aggregation
3. Tool results → Message metadata → Chat display

**Settings Management:**
1. UI changes → `SettingsProvider` → Database/Secure storage
2. Setting changes → Provider invalidation → UI reactive updates
3. App-wide settings → Theme, model parameters, preferences

### Database Schema Design
- **Conversations**: Chat threads with metadata and token tracking
- **Messages**: Chat messages with role-based types and tool call metadata
- **Settings**: Key-value configuration with type-safe access
- **MCP Integration**: Server configurations and tool execution history
- **Full-text Search**: Optimized message search with FTS5

### Code Generation Dependencies
The app heavily relies on code generation for:
- **Drift**: Database schema and DAOs (`.g.dart`, `.drift.dart`)
- **Riverpod**: Providers with `@riverpod` annotation
- **Freezed**: Immutable models with union types
- **JSON Serialization**: API model serialization

Always run `dart run build_runner build` after:
- Adding new `@riverpod` providers
- Modifying Drift tables or DAOs
- Creating/updating Freezed models
- Adding JSON serializable classes
- Finishing your work

### MCP Protocol Implementation
The MCP (Model Context Protocol) implementation provides:
- **JSON-RPC 2.0**: Full client implementation with request/response correlation
- **Transport Layer**: WebSocket and HTTP transport with auto-reconnection
- **Tool Management**: Dynamic tool discovery, validation, and execution
- **Server Management**: Multi-server support with connection state tracking

### OpenRouter Integration
- **Streaming Chat**: Real-time message streaming with SSE parsing
- **Model Management**: Dynamic model discovery and parameter configuration
- **Error Handling**: Comprehensive API error mapping and retry logic
- **Token Counting**: Approximate token estimation for cost tracking

### Responsive Design Patterns
- **Adaptive Scaffold**: Automatically switches between navigation patterns
- **Breakpoint-based**: Mobile (<600px), Tablet (600-1200px), Desktop (>1200px)
- **Navigation**: Bottom bar (mobile), Navigation rail (tablet/desktop)
- **Layout**: Single pane (mobile), Master-detail (tablet+)

### Security Considerations
- **API Keys**: Stored in Flutter Secure Storage, never in database
- **Sensitive Data**: MCP server credentials isolated in secure storage
- **Settings Separation**: Public settings in database, secrets in secure storage