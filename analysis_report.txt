[info] Unnecessary use of multiple underscores (/Users/<USER>/source/otlo/lib/core/mcp/mcp_integration_provider.dart:109:18)
[warning] The value of the local variable 'theme' isn't used (/Users/<USER>/source/otlo/lib/features/settings/screens/settings_screen.dart:9:11)
[warning] The left operand can't be null, so the right operand is never executed (/Users/<USER>/source/otlo/lib/features/chat/providers/chat_provider.dart:189:71)
[warning] The left operand can't be null, so the right operand is never executed (/Users/<USER>/source/otlo/lib/features/chat/providers/chat_provider.dart:275:39)
[warning] The left operand can't be null, so the right operand is never executed (/Users/<USER>/source/otlo/lib/features/chat/providers/chat_provider.dart:277:70)
[warning] The value of the local variable 'existingIds' isn't used (/Users/<USER>/source/otlo/lib/features/chat/providers/chat_provider.dart:249:11)
[info] Statements in an if should be enclosed in a block (/Users/<USER>/source/otlo/lib/features/chat/providers/chat_provider.dart:259:9)
[warning] The value of 'refresh' should be used (/Users/<USER>/source/otlo/lib/features/chat/screens/conversation_list_screen.dart:131:13)
[warning] The declaration '_showModelSwitchMessage' isn't referenced (/Users/<USER>/source/otlo/lib/features/chat/screens/chat_screen.dart:766:8)
[warning] The value of the local variable 'chatNotifier' isn't used (/Users/<USER>/source/otlo/lib/features/chat/screens/chat_screen.dart:767:11)
[warning] The declaration '_handleMenuAction' isn't referenced (/Users/<USER>/source/otlo/lib/features/chat/screens/chat_screen.dart:792:8)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/screens/chat_screen.dart:776:26)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/Users/<USER>/source/otlo/lib/features/chat/screens/chat_screen.dart:857:17)
[warning] The value of the local variable 'colorScheme' isn't used (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:49:11)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:343:27)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:346:29)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:349:29)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:355:27)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/chat/widgets/message_bubble.dart:358:27)
[warning] The value of the local variable 'colorScheme' isn't used (/Users/<USER>/source/otlo/lib/features/chat/widgets/conversation_tile.dart:27:11)
[info] The private field _servers could be 'final' (/Users/<USER>/source/otlo/lib/features/mcp_management/screens/mcp_servers_screen.dart:35:23)
[warning] The value of the local variable 'theme' isn't used (/Users/<USER>/source/otlo/lib/features/mcp_management/screens/tool_browser_screen.dart:155:11)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/mcp_management/screens/tool_browser_screen.dart:614:18)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/source/otlo/lib/features/mcp_management/screens/tool_browser_screen.dart:616:26)
[warning] The value of the local variable 'theme' isn't used (/Users/<USER>/source/otlo/lib/features/models/screens/model_browser_screen.dart:35:11)
[warning] The value of the local variable 'isMobile' isn't used (/Users/<USER>/source/otlo/lib/shared/widgets/adaptive_scaffold.dart:19:11)