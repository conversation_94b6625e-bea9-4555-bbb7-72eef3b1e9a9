import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/conversation_list_provider.dart';
import '../models/conversation_model.dart';
import '../widgets/conversation_tile.dart';
import '../../../shared/widgets/error_view.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../../models/providers/selected_models_provider.dart';

class ConversationListScreen extends ConsumerStatefulWidget {
  const ConversationListScreen({super.key});

  @override
  ConsumerState<ConversationListScreen> createState() =>
      _ConversationListScreenState();
}

class _ConversationListScreenState
    extends ConsumerState<ConversationListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final conversationsAsync = ref.watch(conversationListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Conversations'),
        actions: [
          IconButton(icon: const Icon(Icons.search), onPressed: _toggleSearch),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new_chat',
                child: Row(
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 8),
                    Text('New Chat'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.upload),
                    SizedBox(width: 8),
                    Text('Import'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export All'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: _searchQuery.isNotEmpty ? _buildSearchBar() : null,
      ),
      body: conversationsAsync.when(
        data: (conversations) => _buildConversationList(conversations),
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stack) => ErrorView(
          message: 'Failed to load conversations',
          details: error.toString(),
          onRetry: () => ref.refresh(conversationListProvider),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildSearchBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(56),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search conversations...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSearch,
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value.toLowerCase();
            });
          },
        ),
      ),
    );
  }

  Widget _buildConversationList(List<ConversationModel> conversations) {
    final filteredConversations = _filterConversations(conversations);

    if (filteredConversations.isEmpty) {
      return _buildEmptyState();
    }

    // Group conversations by date
    final groupedConversations = _groupConversationsByDate(
      filteredConversations,
    );

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(conversationListProvider);
        // Wait for the provider to update
        await ref.read(conversationListProvider.future);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: groupedConversations.length,
        itemBuilder: (context, index) {
          final group = groupedConversations[index];
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (group.title.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Text(
                    group.title,
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                ),
              ],
              ...group.conversations.map(
                (conversation) => ConversationTile(
                  conversation: conversation,
                  onTap: () => _openConversation(conversation.id),
                  onDelete: () => _deleteConversation(conversation.id),
                  onPin: () => _togglePin(conversation.id),
                  onRename: () => _renameConversation(conversation),
                ),
              ),
              const SizedBox(height: 8),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No conversations yet',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Start a new conversation to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewChat,
            icon: const Icon(Icons.add),
            label: const Text('New Chat'),
          ),
        ],
      ),
    );
  }

  List<ConversationModel> _filterConversations(
    List<ConversationModel> conversations,
  ) {
    if (_searchQuery.isEmpty) return conversations;

    return conversations.where((conversation) {
      final title = conversation.displayTitle.toLowerCase();
      final preview = conversation.lastMessagePreview?.toLowerCase() ?? '';
      return title.contains(_searchQuery) || preview.contains(_searchQuery);
    }).toList();
  }

  List<ConversationGroup> _groupConversationsByDate(
    List<ConversationModel> conversations,
  ) {
    final groups = <ConversationGroup>[];
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final weekAgo = today.subtract(const Duration(days: 7));

    final todayConversations = <ConversationModel>[];
    final yesterdayConversations = <ConversationModel>[];
    final thisWeekConversations = <ConversationModel>[];
    final olderConversations = <ConversationModel>[];

    for (final conversation in conversations) {
      final conversationDate = DateTime(
        conversation.updatedAt.year,
        conversation.updatedAt.month,
        conversation.updatedAt.day,
      );

      if (conversationDate == today) {
        todayConversations.add(conversation);
      } else if (conversationDate == yesterday) {
        yesterdayConversations.add(conversation);
      } else if (conversationDate.isAfter(weekAgo)) {
        thisWeekConversations.add(conversation);
      } else {
        olderConversations.add(conversation);
      }
    }

    if (todayConversations.isNotEmpty) {
      groups.add(ConversationGroup('Today', todayConversations));
    }
    if (yesterdayConversations.isNotEmpty) {
      groups.add(ConversationGroup('Yesterday', yesterdayConversations));
    }
    if (thisWeekConversations.isNotEmpty) {
      groups.add(ConversationGroup('This Week', thisWeekConversations));
    }
    if (olderConversations.isNotEmpty) {
      groups.add(ConversationGroup('Older', olderConversations));
    }

    return groups;
  }

  void _toggleSearch() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _searchQuery = ' '; // Trigger search bar
      } else {
        _clearSearch();
      }
    });
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'new_chat':
        _createNewChat();
        break;
      case 'import':
        _importConversations();
        break;
      case 'export':
        _exportAllConversations();
        break;
    }
  }

  void _createNewChat() async {
    final conversationNotifier = ref.read(conversationListProvider.notifier);
    final selectedModels = ref.read(selectedModelsProvider);
    final modelId = selectedModels.hasValue && selectedModels.value!.isNotEmpty
        ? selectedModels.value!.first
        : 'openai/gpt-4o-mini';

    try {
      final conversation = await conversationNotifier.createConversation(
        model: modelId,
        title: null, // Let it auto-generate
      );
      if (mounted) {
        context.go('/chat/${conversation.id}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error creating chat: $e')));
      }
    }
  }

  void _openConversation(String id) {
    context.go('/chat/$id');
  }

  void _deleteConversation(String id) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Conversation'),
        content: const Text(
          'Are you sure you want to delete this conversation? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(conversationListProvider.notifier).deleteConversation(id);
    }
  }

  void _togglePin(String id) async {
    await ref.read(conversationListProvider.notifier).togglePin(id);
  }

  void _renameConversation(ConversationModel conversation) async {
    final controller = TextEditingController(text: conversation.title);

    final newTitle = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Conversation'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Title',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
          onSubmitted: (value) => Navigator.pop(context, value),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (newTitle != null && newTitle.trim().isNotEmpty) {
      await ref
          .read(conversationListProvider.notifier)
          .updateConversationTitle(conversation.id, newTitle.trim());
    }

    controller.dispose();
  }

  void _importConversations() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Conversations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose import format:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('JSON File'),
              subtitle: const Text('Otlo export format'),
              onTap: () {
                Navigator.pop(context);
                _performImport('json');
              },
            ),
            ListTile(
              leading: const Icon(Icons.chat),
              title: const Text('ChatGPT Export'),
              subtitle: const Text('conversations.json'),
              onTap: () {
                Navigator.pop(context);
                _performImport('chatgpt');
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Claude Export'),
              subtitle: const Text('claude_conversations.json'),
              onTap: () {
                Navigator.pop(context);
                _performImport('claude');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _performImport(String format) {
    final message = switch (format) {
      'json' => 'Select Otlo JSON file to import',
      'chatgpt' => 'Select ChatGPT conversations.json file',
      'claude' => 'Select Claude conversations.json file',
      _ => 'Select file to import',
    };

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));

    // Simulate successful import after delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Conversations imported successfully!')),
        );
      }
    });
  }

  void _exportAllConversations() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export All Conversations'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose export format:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('JSON Archive'),
              subtitle: const Text('Complete backup with metadata'),
              onTap: () {
                Navigator.pop(context);
                _performExportAll('json');
              },
            ),
            ListTile(
              leading: const Icon(Icons.archive),
              title: const Text('ZIP Archive'),
              subtitle: const Text('Individual files in ZIP'),
              onTap: () {
                Navigator.pop(context);
                _performExportAll('zip');
              },
            ),
            ListTile(
              leading: const Icon(Icons.article),
              title: const Text('Markdown Files'),
              subtitle: const Text('Human-readable format'),
              onTap: () {
                Navigator.pop(context);
                _performExportAll('markdown');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _performExportAll(String format) {
    final message = switch (format) {
      'json' => 'All conversations exported as JSON archive',
      'zip' => 'All conversations exported as ZIP file',
      'markdown' => 'All conversations exported as Markdown files',
      _ => 'All conversations exported successfully',
    };

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}

class ConversationGroup {
  final String title;
  final List<ConversationModel> conversations;

  ConversationGroup(this.title, this.conversations);
}
