import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gpt_markdown/gpt_markdown.dart';

import '../models/message_model.dart';
import '../../../shared/theme/app_colors.dart';
import '../../../shared/widgets/loading_overlay.dart';
import 'tool_call_card.dart';

class MessageBubble extends StatefulWidget {
  final MessageModel message;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onRegenerate;
  final VoidCallback? onCopy;

  const MessageBubble({
    super.key,
    required this.message,
    this.onEdit,
    this.onDelete,
    this.onRegenerate,
    this.onCopy,
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> {
  bool _isEditing = false;
  late TextEditingController _editController;

  @override
  void initState() {
    super.initState();
    _editController = TextEditingController(text: widget.message.content);
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isUser = widget.message.isUser;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            child: Column(
              crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                const SizedBox(height: 4),
                _buildMessageContent(context),
                if (widget.message.metadata?.toolCalls != null) ...[
                  const SizedBox(height: 8),
                  ..._buildToolCalls(context),
                ],
                const SizedBox(height: 4),
                _buildFooter(context),
              ],
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.message.displayRole,
          style: theme.textTheme.labelSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          widget.message.formattedTime,
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isUser = widget.message.isUser;

    Color backgroundColor;
    Color textColor;
    
    if (isUser) {
      backgroundColor = colorScheme.surfaceContainerHighest;
      textColor = colorScheme.onSurfaceVariant;
    } else {
      backgroundColor = Colors.transparent;
      textColor = colorScheme.onSurface;
    }

    if (widget.message.isSystem) {
      backgroundColor = colorScheme.systemMessage;
      textColor = colorScheme.onSurfaceVariant;
    } else if (widget.message.isTool) {
      backgroundColor = colorScheme.toolMessage;
      textColor = colorScheme.onSurfaceVariant;
    }

    // For AI messages (assistant), no bubble - just plain content
    if (widget.message.isAssistant) {
      return GestureDetector(
        onLongPressStart: (details) => _showMessageActions(context, details.globalPosition),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          padding: const EdgeInsets.all(8),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.message.isStreaming) ...[
                    Row(
                      children: [
                        Expanded(
                          child: _buildMessageText(context, textColor),
                        ),
                        const SizedBox(width: 8),
                        const StreamingIndicator(),
                      ],
                    ),
                  ] else
                    _buildMessageText(context, textColor),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // For user messages and system/tool messages - keep bubble
    return GestureDetector(
      onLongPressStart: (details) => _showMessageActions(context, details.globalPosition),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(20),
            topRight: const Radius.circular(20),
            bottomLeft: isUser ? const Radius.circular(20) : const Radius.circular(6),
            bottomRight: isUser ? const Radius.circular(6) : const Radius.circular(20),
          ),
          border: widget.message.isSystem || widget.message.isTool
              ? Border.all(color: colorScheme.border, width: 1)
              : null,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.message.isStreaming) ...[
                  Row(
                    children: [
                      Expanded(
                        child: _buildMessageText(context, textColor),
                      ),
                      const SizedBox(width: 8),
                      const StreamingIndicator(),
                    ],
                  ),
                ] else
                  _buildMessageText(context, textColor),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageText(BuildContext context, Color textColor) {
    final theme = Theme.of(context);
    
    // If editing user message, show text field
    if (_isEditing && widget.message.isUser) {
      return TextField(
        controller: _editController,
        autofocus: true,
        maxLines: null,
        style: theme.textTheme.bodyMedium?.copyWith(color: textColor),
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        onSubmitted: (value) {
          _saveEdit();
        },
      );
    }
    
    if (widget.message.isUser || widget.message.isSystem) {
      return SelectableText(
        widget.message.content,
        style: theme.textTheme.bodyMedium?.copyWith(color: textColor),
      );
    } else {
      // Use GPT Markdown for assistant messages
      return GptMarkdown(
        widget.message.content,
        style: theme.textTheme.bodyMedium?.copyWith(color: textColor),
      );
    }
  }
  
  void _saveEdit() {
    setState(() {
      _isEditing = false;
    });
    // Call the onEdit callback with the new content
    if (widget.onEdit != null) {
      widget.onEdit!();
    }
  }
  
  void _startEditing() {
    setState(() {
      _isEditing = true;
    });
  }


  List<Widget> _buildToolCalls(BuildContext context) {
    final toolCalls = widget.message.metadata?.toolCalls ?? [];
    return toolCalls.map((toolCall) => Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: ToolCallCard(toolCall: toolCall),
    )).toList();
  }

  Widget _buildFooter(BuildContext context) {
    return const SizedBox.shrink();
  }

  void _showMessageActions(BuildContext context, Offset tapPosition) {
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    
    showMenu(
      context: context,
      position: RelativeRect.fromRect(
        tapPosition & const Size(40, 40),
        Offset.zero & overlay.size,
      ),
      items: [
        const PopupMenuItem(
          value: 'copy',
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16),
              SizedBox(width: 8),
              Text('Copy'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'prev_thread',
          child: Row(
            children: [
              Icon(Icons.chevron_left, size: 16),
              SizedBox(width: 8),
              Text('Previous thread'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'next_thread',
          child: Row(
            children: [
              Icon(Icons.chevron_right, size: 16),
              SizedBox(width: 8),
              Text('Next thread'),
            ],
          ),
        ),
        if (widget.message.isUser && widget.onEdit != null)
          const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 16),
                SizedBox(width: 8),
                Text('Edit'),
              ],
            ),
          ),
        if (widget.message.isAssistant && widget.onRegenerate != null)
          const PopupMenuItem(
            value: 'regenerate',
            child: Row(
              children: [
                Icon(Icons.refresh, size: 16),
                SizedBox(width: 8),
                Text('Regenerate'),
              ],
            ),
          ),
        if (widget.onDelete != null)
          PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Theme.of(context).colorScheme.error),
                const SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Theme.of(context).colorScheme.error)),
              ],
            ),
          ),
      ],
    ).then((value) {
      if (value != null && mounted) {
        switch (value) {
          case 'copy':
            _handleAction(context, 'copy');
            break;
          case 'prev_thread':
            _navigateThread(context, -1);
            break;
          case 'next_thread':
            _navigateThread(context, 1);
            break;
          case 'edit':
            _startEditing();
            break;
          case 'regenerate':
            _handleAction(context, 'regenerate');
            break;
          case 'delete':
            _handleAction(context, 'delete');
            break;
        }
      }
    });
  }
  
  void _navigateThread(BuildContext context, int direction) {
    // Placeholder for thread navigation
    final directionText = direction > 0 ? 'next' : 'previous';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to $directionText thread not implemented yet'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'copy':
        _copyToClipboard(context);
        break;
      case 'edit':
        widget.onEdit?.call();
        break;
      case 'regenerate':
        widget.onRegenerate?.call();
        break;
      case 'delete':
        widget.onDelete?.call();
        break;
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: widget.message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
    widget.onCopy?.call();
  }

}