import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/api/openrouter/models_api.dart';
import '../../settings/providers/settings_provider.dart';

part 'models_provider.g.dart';

@riverpod
class OpenRouterModels extends _$OpenRouterModels {
  OpenRouterModelsApi? _api;

  @override
  Future<List<OpenRouterModel>> build() async {
    final settings = await ref.watch(settingsProvider.future);
    final apiKey = settings['openrouter_api_key'] as String?;
    
    _api = OpenRouterModelsApi(apiKey: apiKey);
    return await _api!.fetchAllModels();
  }

  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

@riverpod
class ActiveModels extends _$ActiveModels {
  @override
  Future<List<ActiveModel>> build() async {
    final settings = await ref.watch(settingsProvider.future);
    final activeModelsJson = settings['active_models'] as String?;
    
    if (activeModelsJson == null) {
      // Return default models if none configured
      return _getDefaultActiveModels();
    }
    
    try {
      final List<dynamic> decoded = jsonDecode(activeModelsJson);
      final activeModels = decoded
          .map((json) => ActiveModel.fromJson(json))
          .toList();
      
      // Sort by order
      activeModels.sort((a, b) => a.order.compareTo(b.order));
      return activeModels;
    } catch (e) {
      return _getDefaultActiveModels();
    }
  }

  List<ActiveModel> _getDefaultActiveModels() {
    // Return empty list - deprecated in favor of selected models
    return [];
  }

  Future<void> addActiveModel(OpenRouterModel model, {String? nickname}) async {
    final current = await future;
    final newOrder = current.length;
    
    final activeModel = ActiveModel(
      model: model,
      nickname: nickname,
      order: newOrder,
    );
    
    final updated = [...current, activeModel];
    await _saveActiveModels(updated);
  }

  Future<void> removeActiveModel(String modelId) async {
    final current = await future;
    final updated = current.where((m) => m.model.id != modelId).toList();
    
    // Reorder
    for (int i = 0; i < updated.length; i++) {
      updated[i] = updated[i].copyWith(order: i);
    }
    
    await _saveActiveModels(updated);
  }

  Future<void> updateActiveModel(String modelId, ActiveModel updatedModel) async {
    final current = await future;
    final index = current.indexWhere((m) => m.model.id == modelId);
    
    if (index != -1) {
      final updated = [...current];
      updated[index] = updatedModel;
      await _saveActiveModels(updated);
    }
  }

  Future<void> reorderActiveModels(List<ActiveModel> newOrder) async {
    final updated = <ActiveModel>[];
    for (int i = 0; i < newOrder.length; i++) {
      updated.add(newOrder[i].copyWith(order: i));
    }
    await _saveActiveModels(updated);
  }

  Future<void> setDefaultModel(String modelId) async {
    final current = await future;
    final modelIndex = current.indexWhere((m) => m.model.id == modelId);
    
    if (modelIndex != -1 && modelIndex != 0) {
      final updated = [...current];
      final model = updated.removeAt(modelIndex);
      updated.insert(0, model.copyWith(order: 0));
      
      // Reorder the rest
      for (int i = 1; i < updated.length; i++) {
        updated[i] = updated[i].copyWith(order: i);
      }
      
      await _saveActiveModels(updated);
    }
  }

  Future<void> _saveActiveModels(List<ActiveModel> models) async {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    final json = jsonEncode(models.map((m) => m.toJson()).toList());
    await settingsNotifier.updateSetting('active_models', json);
    ref.invalidateSelf();
  }

  ActiveModel? getDefaultModel() {
    return state.whenOrNull(data: (models) => models.isNotEmpty ? models.first : null);
  }

  ActiveModel? getModelById(String modelId) {
    final models = state.whenOrNull(data: (models) => models);
    if (models == null) return null;
    
    try {
      return models.firstWhere((m) => m.model.id == modelId);
    } catch (e) {
      return null;
    }
  }
}

@riverpod
class ModelSearch extends _$ModelSearch {
  @override
  List<OpenRouterModel> build(String query, List<String> filters) {
    final allModels = ref.watch(openRouterModelsProvider);
    
    return allModels.when(
      data: (models) => _filterModels(models, query, filters),
      loading: () => [],
      error: (_, _) => [],
    );
  }

  List<OpenRouterModel> _filterModels(
    List<OpenRouterModel> models, 
    String query, 
    List<String> filters,
  ) {
    var filtered = models.where((model) {
      // Text search
      if (query.isNotEmpty) {
        final searchText = query.toLowerCase();
        if (!model.name.toLowerCase().contains(searchText) &&
            !model.id.toLowerCase().contains(searchText) &&
            !model.description.toLowerCase().contains(searchText)) {
          return false;
        }
      }
      
      // Apply filters
      for (final filter in filters) {
        switch (filter) {
          case 'vision':
            if (!model.supportsVision) return false;
            break;
          case '32k+':
            if (model.contextLength < 32000) return false;
            break;
          case '128k+':
            if (model.contextLength < 128000) return false;
            break;
          case 'function_calling':
            if (!model.supportsFunctionCalling) return false;
            break;
          case 'under_5':
            if (model.inputPricePerMillion >= 5 && model.outputPricePerMillion >= 5) {
              return false;
            }
            break;
        }
      }
      
      return true;
    }).toList();
    
    // Sort by relevance (exact matches first, then by price)
    filtered.sort((a, b) {
      if (query.isNotEmpty) {
        final aExactMatch = a.name.toLowerCase() == query.toLowerCase() ||
                           a.id.toLowerCase() == query.toLowerCase();
        final bExactMatch = b.name.toLowerCase() == query.toLowerCase() ||
                           b.id.toLowerCase() == query.toLowerCase();
        
        if (aExactMatch && !bExactMatch) return -1;
        if (bExactMatch && !aExactMatch) return 1;
      }
      
      // Sort by input price
      return a.inputPricePerMillion.compareTo(b.inputPricePerMillion);
    });
    
    return filtered;
  }
}