import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../models/providers/models_provider.dart';
import '../../models/providers/selected_models_provider.dart';
import '../../../core/api/openrouter/models_api.dart';

/// OpenRouter models management screen within settings.
///
/// This screen provides a professional interface for users to browse, search,
/// and select OpenRouter models. Features include:
/// - Category filtering: ALL, FREE, VISION, AUDIO
/// - Fuzzy search functionality for model discovery
/// - Persistent model selection stored in user settings
/// - Professional UI without gradients or excessive styling
class OpenRouterModelsScreen extends ConsumerStatefulWidget {
  const OpenRouterModelsScreen({super.key});

  @override
  ConsumerState<OpenRouterModelsScreen> createState() =>
      _OpenRouterModelsScreenState();
}

class _OpenRouterModelsScreenState
    extends ConsumerState<OpenRouterModelsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'ALL';

  /// Available model categories for filtering
  static const _categories = ['ALL', 'FREE', 'VISION', 'AUDIO'];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final allModelsAsync = ref.watch(openRouterModelsProvider);
    final selectedModelsAsync = ref.watch(selectedModelsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('OpenRouter Models'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        children: [
          // Search and filter section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Column(
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search models by name, ID, or description',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
                      alpha: 0.3,
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
                const SizedBox(height: 12),

                // Category filters
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _categories.map((category) {
                      final isSelected = _selectedCategory == category;

                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                          backgroundColor: theme.colorScheme.surface,
                          selectedColor: theme.colorScheme.primaryContainer,
                          labelStyle: TextStyle(
                            color: isSelected
                                ? theme.colorScheme.onPrimaryContainer
                                : theme.colorScheme.onSurface,
                            fontSize: 12,
                            fontWeight: isSelected
                                ? FontWeight.w500
                                : FontWeight.normal,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),

          // Models list
          Expanded(
            child: allModelsAsync.when(
              data: (allModels) => selectedModelsAsync.when(
                data: (selectedModels) =>
                    _buildModelsList(allModels, selectedModels, theme),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildErrorView(error, theme),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => _buildErrorView(error, theme),
            ),
          ),

          // Footer with selection count
          selectedModelsAsync.when(
            data: (selectedModels) => Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${selectedModels.length} models selected',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (selectedModels.isNotEmpty)
                    TextButton(
                      onPressed: () => _showClearAllDialog(context),
                      child: const Text('Clear All'),
                    ),
                ],
              ),
            ),
            loading: () => const SizedBox(height: 50),
            error: (_, _) => const SizedBox(height: 50),
          ),
        ],
      ),
    );
  }

  /// Builds the filtered and categorized models list
  Widget _buildModelsList(
    List<OpenRouterModel> allModels,
    List<String> selectedModels,
    ThemeData theme,
  ) {
    final filteredModels = _filterModels(allModels);

    if (filteredModels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No models found',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter criteria',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    // Group models by provider
    final groupedModels = _groupModelsByProvider(filteredModels);

    return ListView.builder(
      itemCount: groupedModels.length,
      itemBuilder: (context, groupIndex) {
        final group = groupedModels[groupIndex];
        return _buildModelGroup(group, selectedModels, theme);
      },
    );
  }

  /// Builds a provider group section with models
  Widget _buildModelGroup(
    _ModelGroup group,
    List<String> selectedModels,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (group.title.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              group.title,
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        ...group.models.map(
          (model) =>
              _buildModelTile(model, selectedModels.contains(model.id), theme),
        ),
      ],
    );
  }

  /// Builds an individual model selection tile
  Widget _buildModelTile(
    OpenRouterModel model,
    bool isSelected,
    ThemeData theme,
  ) {
    final isFree =
        model.inputPricePerMillion == 0 && model.outputPricePerMillion == 0;

    return ListTile(
      title: Text(
        model.name,
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            model.id,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(height: 2),
          Text(
            'Context: ${model.contextSizeLabel}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isFree)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.green, width: 1),
              ),
              child: Text(
                'FREE',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(
              isSelected ? Icons.check_circle : Icons.add_circle_outline,
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurfaceVariant,
            ),
            onPressed: () {
              ref.read(selectedModelsProvider.notifier).toggleModel(model.id);
            },
          ),
        ],
      ),
      onTap: () {
        ref.read(selectedModelsProvider.notifier).toggleModel(model.id);
      },
    );
  }

  /// Builds error state widget
  Widget _buildErrorView(dynamic error, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(
            'Failed to load models',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please check your API key and connection',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.refresh(openRouterModelsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Filters models based on search query and category
  List<OpenRouterModel> _filterModels(List<OpenRouterModel> models) {
    var filtered = models.where((model) {
      // Fuzzy text search across multiple fields
      if (_searchQuery.isNotEmpty) {
        final searchTerms = _searchQuery
            .split(' ')
            .where((term) => term.isNotEmpty);
        final searchableText = '${model.name} ${model.id} ${model.description}'
            .toLowerCase();

        // Check if all search terms are present in the searchable text
        final matchesSearch = searchTerms.every(
          (term) =>
              searchableText.contains(term) ||
              _fuzzyMatch(searchableText, term),
        );

        if (!matchesSearch) return false;
      }

      // Category filtering
      switch (_selectedCategory) {
        case 'FREE':
          return model.inputPricePerMillion == 0 &&
              model.outputPricePerMillion == 0;
        case 'VISION':
          return model.supportsVision;
        case 'AUDIO':
          return _isAudioModel(model);
        default: // 'ALL'
          return true;
      }
    }).toList();

    // Sort by name for consistent ordering
    filtered.sort((a, b) => a.name.compareTo(b.name));

    return filtered;
  }

  /// Simple fuzzy matching for search terms
  bool _fuzzyMatch(String text, String pattern) {
    if (pattern.length <= 2) return false; // Only fuzzy match longer terms

    int patternIndex = 0;
    for (int i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] == pattern[patternIndex]) {
        patternIndex++;
      }
    }
    return patternIndex >= pattern.length - 1; // Allow 1 character tolerance
  }

  /// Determines if a model supports audio functionality
  bool _isAudioModel(OpenRouterModel model) {
    final audioKeywords = [
      'audio',
      'speech',
      'voice',
      'tts',
      'stt',
      'whisper',
      'realtime',
    ];
    final searchText = '${model.name} ${model.id} ${model.description}'
        .toLowerCase();

    return audioKeywords.any((keyword) => searchText.contains(keyword));
  }

  /// Groups models by their provider for organized display
  List<_ModelGroup> _groupModelsByProvider(List<OpenRouterModel> models) {
    final Map<String, List<OpenRouterModel>> groupedMap = {};

    for (final model in models) {
      final provider = _getProviderFromId(model.id);
      if (!groupedMap.containsKey(provider)) {
        groupedMap[provider] = [];
      }
      groupedMap[provider]!.add(model);
    }

    final groups = <_ModelGroup>[];
    final sortedKeys = groupedMap.keys.toList()..sort();

    for (final provider in sortedKeys) {
      groups.add(
        _ModelGroup(_formatProviderTitle(provider), groupedMap[provider]!),
      );
    }

    return groups;
  }

  /// Extracts provider name from model ID
  String _getProviderFromId(String modelId) {
    final parts = modelId.split('/');
    return parts.isNotEmpty ? parts.first : 'Unknown';
  }

  /// Formats provider name for display
  String _formatProviderTitle(String provider) {
    switch (provider.toLowerCase()) {
      case 'openai':
        return 'OpenAI';
      case 'anthropic':
        return 'Anthropic';
      case 'meta-llama':
        return 'Meta Llama';
      case 'google':
        return 'Google';
      case 'cohere':
        return 'Cohere';
      case 'mistralai':
        return 'Mistral AI';
      case 'huggingfaceh4':
        return 'Hugging Face';
      default:
        return provider.toUpperCase();
    }
  }

  /// Shows confirmation dialog for clearing all selected models
  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Models'),
        content: const Text(
          'Are you sure you want to remove all selected models?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(selectedModelsProvider.notifier).clearAll();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

/// Helper class for organizing models by provider
class _ModelGroup {
  final String title;
  final List<OpenRouterModel> models;

  _ModelGroup(this.title, this.models);
}
