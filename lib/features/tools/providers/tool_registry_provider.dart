import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/mcp/tools/tool_registry.dart';
import '../../../core/mcp/protocol/mcp_types.dart';

part 'tool_registry_provider.g.dart';

/// Global tool registry instance
/// This provides access to all MCP tools from connected servers
@riverpod
class ToolRegistryNotifier extends _$ToolRegistryNotifier {
  late ToolRegistry _registry;

  @override
  ToolRegistry build() {
    _registry = ToolRegistry();
    
    // Listen for tool updates and refresh state
    _registry.events.listen((event) {
      if (event is ToolsUpdatedEvent) {
        // Notify listeners that tools have changed
        ref.invalidateSelf();
      }
    });
    
    return _registry;
  }

  /// Get all available tools from all connected MCP servers
  List<McpTool> getAllTools() {
    final allTools = <McpTool>[];
    
    for (final toolWithServer in _registry.allTools) {
      allTools.add(toolWithServer.tool);
    }
    
    return allTools;
  }

  /// Execute a tool by name
  /// Automatically finds the correct server for the tool
  Future<McpToolCallResult> executeTool({
    required String toolName,
    Map<String, dynamic>? arguments,
  }) async {
    final toolWithServer = _registry.findTool(toolName);
    if (toolWithServer == null) {
      throw Exception('Tool $toolName not found');
    }

    return await _registry.executeTool(
      serverId: toolWithServer.serverId,
      toolName: toolName,
      arguments: arguments,
    );
  }

  /// Register a new MCP server
  Future<void> registerServer(String serverId, dynamic mcpClient) async {
    await _registry.registerServer(serverId, mcpClient);
    ref.invalidateSelf();
  }

  /// Unregister an MCP server
  Future<void> unregisterServer(String serverId) async {
    await _registry.unregisterServer(serverId);
    ref.invalidateSelf();
  }
}

/// Provider for accessing the tool registry
@riverpod
ToolRegistry toolRegistry(Ref ref) {
  return ref.watch(toolRegistryNotifierProvider);
}

/// Provider for getting all available tools
@riverpod
List<McpTool> availableTools(Ref ref) {
  final registry = ref.watch(toolRegistryNotifierProvider.notifier);
  return registry.getAllTools();
}