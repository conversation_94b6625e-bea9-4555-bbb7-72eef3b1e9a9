import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:dio/dio.dart';

import '../base/api_client.dart';
import '../base/streaming_client.dart';
import '../base/api_exceptions.dart';
import 'openrouter_models.dart';

class OpenRouterClient extends ApiClient with StreamingClient {
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  String? _apiKey;
  String? _appName;

  OpenRouterClient({
    String? apiKey,
    String? appName,
  }) : super(
          baseUrl: _baseUrl,
          defaultHeaders: {
            'Content-Type': 'application/json',
          },
          timeout: const Duration(seconds: 60),
        ) {
    setCredentials(apiKey: apiKey, appName: appName);
  }

  void setCredentials({String? apiKey, String? appName}) {
    _apiKey = apiKey;
    _appName = appName;
    
    if (_apiKey != null) {
      setAuthHeader(_apiKey!);
    }
    
    if (_appName != null) {
      dio.options.headers['HTTP-Referer'] = _appName;
      dio.options.headers['X-Title'] = _appName;
    }
  }

  @override
  void onRequest(RequestOptions options) {
    // Log request details for debugging
    developer.log('${options.method} ${options.uri}', name: 'OpenRouterClient.request');
  }

  @override
  void onError(ApiException error) {
    // Log errors for debugging
    developer.log('API Error: $error', name: 'OpenRouterClient.error', level: 1000);
  }

  // Get available models
  Future<List<ModelInfo>> getModels() async {
    try {
      final response = await get('/models');
      final data = response.data as Map<String, dynamic>;
      final models = data['data'] as List;
      
      return models
          .map((model) => ModelInfo.fromJson(model as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw _handleUnexpectedError(e);
    }
  }

  // Non-streaming chat completion
  Future<ChatCompletionResponse> createChatCompletion(
    ChatCompletionRequest request,
  ) async {
    try {
      final response = await post(
        '/chat/completions',
        data: request.copyWith(stream: false).toJson(),
      );
      
      return ChatCompletionResponse.fromJson(
        response.data as Map<String, dynamic>,
      );
    } catch (e) {
      throw _handleUnexpectedError(e);
    }
  }

  // Streaming chat completion
  Stream<ChatCompletionChunk> createChatCompletionStream(
    ChatCompletionRequest request, {
    CancellationToken? cancellationToken,
  }) async* {
    try {
      final responseBody = await postStream(
        '/chat/completions',
        data: request.copyWith(stream: true).toJson(),
      );

      final stream = parseSSE(responseBody.stream)
          .transform(StreamTransformer<String, ChatCompletionChunk>.fromHandlers(
            handleData: (data, sink) {
              try {
                if (data.trim() == '[DONE]') {
                  return;
                }
                
                final json = jsonDecode(data) as Map<String, dynamic>;
                final chunk = ChatCompletionChunk.fromJson(json);
                sink.add(chunk);
              } catch (e) {
                sink.addError(ServerException(
                  message: 'Failed to parse streaming response: $e',
                ));
              }
            },
            handleError: (error, stackTrace, sink) {
              sink.addError(_handleUnexpectedError(error));
            },
          ));

      if (cancellationToken != null) {
        yield* cancellable(stream, cancellationToken);
      } else {
        yield* stream;
      }
    } catch (e) {
      throw _handleUnexpectedError(e);
    }
  }

  // Generate text with simplified interface
  Future<String> generateText({
    required String model,
    required String prompt,
    double? temperature,
    int? maxTokens,
    List<String>? stop,
  }) async {
    final request = ChatCompletionRequest(
      model: model,
      messages: [
        ChatMessage(role: 'user', content: prompt),
      ],
      temperature: temperature,
      maxTokens: maxTokens,
      stop: stop,
    );

    final response = await createChatCompletion(request);
    return response.choices.first.message.content;
  }

  // Generate text stream with simplified interface
  Stream<String> generateTextStream({
    required String model,
    required String prompt,
    double? temperature,
    int? maxTokens,
    List<String>? stop,
    CancellationToken? cancellationToken,
  }) async* {
    final request = ChatCompletionRequest(
      model: model,
      messages: [
        ChatMessage(role: 'user', content: prompt),
      ],
      temperature: temperature,
      maxTokens: maxTokens,
      stop: stop,
    );

    await for (final chunk in createChatCompletionStream(
      request,
      cancellationToken: cancellationToken,
    )) {
      final content = chunk.choices.firstOrNull?.delta.content;
      if (content != null && content.isNotEmpty) {
        yield content;
      }
    }
  }

  // Count tokens (approximate)
  int estimateTokenCount(String text) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    // This is a simplified approach - for accurate counting, you'd need tiktoken
    return (text.length / 4).ceil();
  }

  ApiException _handleUnexpectedError(dynamic error) {
    if (error is ApiException) {
      return error;
    } else if (error is DioException) {
      // This should be handled by the interceptor, but just in case
      return NetworkException(
        message: 'Network error: ${error.message}',
        details: error.toString(),
      );
    } else {
      return ServerException(
        message: 'Unexpected error: $error',
        details: error.toString(),
      );
    }
  }
}