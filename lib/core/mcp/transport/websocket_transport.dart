import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;

import 'package:web_socket_channel/web_socket_channel.dart';

class McpWebSocketTransport {
  WebSocketChannel? _channel;
  final StreamController<String> _messageController = StreamController<String>.broadcast();
  Timer? _pingTimer;
  Timer? _reconnectTimer;
  
  final String _url;
  final Duration _pingInterval;
  final Duration _reconnectInterval;
  final int _maxReconnectAttempts;
  
  int _reconnectAttempts = 0;
  bool _isConnected = false;
  bool _shouldReconnect = true;

  Stream<String> get messages => _messageController.stream;
  bool get isConnected => _isConnected;

  McpWebSocketTransport({
    required String url,
    Duration pingInterval = const Duration(seconds: 30),
    Duration reconnectInterval = const Duration(seconds: 5),
    int maxReconnectAttempts = 5,
  }) : _url = url,
       _pingInterval = pingInterval,
       _reconnectInterval = reconnectInterval,
       _maxReconnectAttempts = maxReconnectAttempts;

  Future<void> connect() async {
    if (_isConnected) {
      return;
    }

    try {
      _channel = WebSocketChannel.connect(Uri.parse(_url));
      
      // Listen for messages
      _channel!.stream.listen(
        (message) {
          _handleMessage(message.toString());
        },
        onError: (error) {
          _handleError(error);
        },
        onDone: () {
          _handleDisconnection();
        },
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      _startPingTimer();
      
    } catch (e) {
      _handleConnectionError(e);
    }
  }

  void sendMessage(String message) {
    if (!_isConnected || _channel == null) {
      throw StateError('WebSocket not connected');
    }

    _channel!.sink.add(message);
  }

  void _handleMessage(String message) {
    _messageController.add(message);
  }

  void _handleError(dynamic error) {
    developer.log('WebSocket error: $error', name: 'McpWebSocketTransport.error', level: 1000);
    _messageController.addError(McpTransportException('WebSocket error: $error'));
  }

  void _handleDisconnection() {
    _isConnected = false;
    _stopPingTimer();
    
    if (_shouldReconnect && _reconnectAttempts < _maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  void _handleConnectionError(dynamic error) {
    developer.log('WebSocket connection error: $error', name: 'McpWebSocketTransport.error', level: 1000);
    _messageController.addError(McpTransportException('Connection failed: $error'));
    
    if (_shouldReconnect && _reconnectAttempts < _maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  void _scheduleReconnect() {
    _reconnectAttempts++;
    
    _reconnectTimer = Timer(_reconnectInterval, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  void _startPingTimer() {
    _pingTimer = Timer.periodic(_pingInterval, (timer) {
      _sendPing();
    });
  }

  void _stopPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = null;
  }

  void _sendPing() {
    try {
      // Send a JSON-RPC ping notification
      final ping = {
        'jsonrpc': '2.0',
        'method': 'notifications/ping',
      };
      sendMessage(jsonEncode(ping));
    } catch (e) {
      developer.log('Failed to send ping: $e', name: 'McpWebSocketTransport.error', level: 1000);
    }
  }

  Future<void> disconnect() async {
    _shouldReconnect = false;
    _stopPingTimer();
    _reconnectTimer?.cancel();
    
    if (_channel != null) {
      await _channel!.sink.close();
      _channel = null;
    }
    
    _isConnected = false;
  }

  void dispose() {
    disconnect();
    _messageController.close();
  }
}

class McpTransportException implements Exception {
  final String message;

  const McpTransportException(this.message);

  @override
  String toString() => 'McpTransportException: $message';
}