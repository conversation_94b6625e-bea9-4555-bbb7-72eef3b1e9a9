import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;

import 'package:dio/dio.dart';

class McpHttpTransport {
  final Dio _dio;
  final String _baseUrl;
  final Duration _timeout;

  McpHttpTransport({
    required String baseUrl,
    Duration timeout = const Duration(seconds: 30),
    Map<String, String>? headers,
  }) : _baseUrl = baseUrl,
       _timeout = timeout,
       _dio = Dio() {
    
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = _timeout;
    _dio.options.receiveTimeout = _timeout;
    _dio.options.headers['Content-Type'] = 'application/json';
    
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          developer.log('MCP HTTP Request: ${options.method} ${options.uri}', name: 'McpHttpTransport');
          handler.next(options);
        },
        onResponse: (response, handler) {
          developer.log('MCP HTTP Response: ${response.statusCode}', name: 'McpHttpTransport');
          handler.next(response);
        },
        onError: (error, handler) {
          developer.log('MCP HTTP Error: ${error.message}', name: 'McpHttpTransport', level: 1000);
          handler.next(error);
        },
      ),
    );
  }

  Future<String> sendMessage(String message) async {
    try {
      final response = await _dio.post(
        '/mcp',
        data: message,
        options: Options(
          headers: {'Content-Type': 'application/json'},
        ),
      );

      if (response.statusCode == 200) {
        if (response.data is String) {
          return response.data as String;
        } else {
          return jsonEncode(response.data);
        }
      } else {
        throw McpHttpTransportException(
          'HTTP ${response.statusCode}: ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      throw McpHttpTransportException(_handleDioError(e));
    } catch (e) {
      throw McpHttpTransportException('Unexpected error: $e');
    }
  }

  String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout';
      
      case DioExceptionType.connectionError:
        return 'Connection error: ${error.message}';
      
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final data = error.response?.data;
        return 'HTTP $statusCode: ${data ?? error.message}';
      
      case DioExceptionType.cancel:
        return 'Request cancelled';
      
      case DioExceptionType.unknown:
      default:
        return error.message ?? 'Unknown error';
    }
  }

  void setAuthHeader(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  void removeAuthHeader() {
    _dio.options.headers.remove('Authorization');
  }

  void dispose() {
    _dio.close();
  }
}

class McpHttpTransportException implements Exception {
  final String message;

  const McpHttpTransportException(this.message);

  @override
  String toString() => 'McpHttpTransportException: $message';
}