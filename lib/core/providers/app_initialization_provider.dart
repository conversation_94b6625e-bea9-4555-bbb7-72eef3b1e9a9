import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../database/tables/mcp_servers.dart';
import '../mcp/mcp_integration_provider.dart';
import '../mcp/mcp_server_manager.dart';
import '../database/daos/mcp_servers_dao.dart';

part 'app_initialization_provider.g.dart';

/// Provider that handles app initialization including setting up default MCP servers
@riverpod
class AppInitialization extends _$AppInitialization {
  @override
  Future<bool> build() async {
    await _setupDefaultMcpServers();
    await _initializeMcpIntegration();
    return true;
  }

  /// Set up default MCP servers if they don't exist
  Future<void> _setupDefaultMcpServers() async {
    final dao = ref.read(mcpServersDaoProvider);
    final existingServers = await dao.getAllServers();
    
    // If no servers exist, add default ones
    if (existingServers.isEmpty) {
      // Add Context7 MCP server
      await dao.createServer(
        id: 'context7',
        name: 'Context7',
        url: 'https://mcp.context7.ai',
        transportType: McpTransportType.sse,
        description: 'Context7 MCP server for documentation and code examples',
      );

      // Add a local built-in tools server (we'll create this next)
      await dao.createServer(
        id: 'builtin_tools',
        name: 'Built-in Tools',
        url: 'builtin://tools',
        transportType: McpTransportType.websocket,
        description: 'Built-in tools for file operations and basic utilities',
      );
    }
  }

  /// Initialize MCP integration
  Future<void> _initializeMcpIntegration() async {
    // This will trigger the MCP integration provider which connects
    // server manager to tool registry
    ref.read(mcpIntegrationProvider);
  }

  /// Add Context7 server manually (for testing)
  Future<void> addContext7Server() async {
    final integration = ref.read(mcpIntegrationProvider.notifier);
    await integration.addContext7Server();
  }

  /// Add Jina server manually (for testing)
  Future<void> addJinaServer() async {
    final integration = ref.read(mcpIntegrationProvider.notifier);
    await integration.addJinaServer();
  }

  /// Get current server connection status
  Map<String, McpServerManagerState>? getServerStates() {
    return ref.read(mcpIntegrationProvider.notifier).serverStates;
  }

  /// Get list of connected servers
  List<String> getConnectedServerIds() {
    return ref.read(mcpIntegrationProvider.notifier).connectedServerIds;
  }
}