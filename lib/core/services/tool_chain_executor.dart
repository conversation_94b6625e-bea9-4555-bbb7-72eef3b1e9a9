import 'dart:convert';

import '../api/openrouter/openrouter_models.dart';
import '../api/openrouter/openrouter_client.dart';
import '../../features/tools/providers/tool_registry_provider.dart';
import '../services/tool_adapter.dart';

/// Executes tool calling chains by managing conversation state and sequential tool calls
class ToolChainExecutor {
  final OpenRouterClient _apiClient;
  final ToolRegistryNotifier _toolRegistry;

  ToolChainExecutor({
    required OpenRouterClient apiClient,
    required ToolRegistryNotifier toolRegistry,
  }) : _apiClient = apiClient,
       _toolRegistry = toolRegistry;

  /// Execute a tool calling chain for a chat completion request
  /// This handles the full conversation flow with sequential tool calls
  Future<ToolChainResult> executeToolChain({
    required ChatCompletionRequest request,
    int maxIterations = 10,
  }) async {
    final List<ChatMessage> conversationHistory = [...request.messages];
    final List<ToolExecutionStep> executionSteps = [];
    int iteration = 0;

    // Get available tools
    final availableTools = _toolRegistry.getAllTools();
    final openRouterTools = ToolAdapter.convertMcpToolsToOpenRouterTools(
      availableTools,
    );

    // Create request with tools
    final requestWithTools = request.copyWith(tools: openRouterTools);

    while (iteration < maxIterations) {
      iteration++;

      // Make API request
      final response = await _apiClient.createChatCompletion(
        requestWithTools.copyWith(messages: conversationHistory),
      );

      final choice = response.choices.first;
      final message = choice.message;

      // Add assistant response to conversation history
      conversationHistory.add(message);

      // Check if there are tool calls
      if (message.toolCalls != null && message.toolCalls!.isNotEmpty) {
        final toolCalls = message.toolCalls!;
        final toolResults = <ChatMessage>[];

        // Execute each tool call
        for (final toolCall in toolCalls) {
          final step = await _executeToolCall(toolCall);
          executionSteps.add(step);

          // Add tool result to conversation history
          final toolResultChatMessage = ChatMessage(
            role: 'tool',
            content: step.result.success
                ? (step.result.content ?? '')
                : 'Error: ${step.result.error}',
            toolCallId: toolCall.id,
            name: toolCall.function.name,
          );

          toolResults.add(toolResultChatMessage);
          conversationHistory.add(toolResultChatMessage);
        }

        // Continue conversation with tool results
        continue;
      } else {
        // No more tool calls, return final result
        return ToolChainResult(
          finalResponse: response,
          conversationHistory: conversationHistory,
          executionSteps: executionSteps,
          iterations: iteration,
          completed: true,
        );
      }
    }

    // Reached maximum iterations
    throw ToolChainException(
      'Tool chain exceeded maximum iterations ($maxIterations)',
      conversationHistory: conversationHistory,
      executionSteps: executionSteps,
    );
  }

  /// Execute a streaming tool calling chain
  Stream<ToolChainStreamEvent> executeStreamingToolChain({
    required ChatCompletionRequest request,
    int maxIterations = 10,
  }) async* {
    final List<ChatMessage> conversationHistory = [...request.messages];
    final List<ToolExecutionStep> executionSteps = [];
    int iteration = 0;

    // Get available tools
    final availableTools = _toolRegistry.getAllTools();
    final openRouterTools = ToolAdapter.convertMcpToolsToOpenRouterTools(
      availableTools,
    );

    // Create request with tools
    final requestWithTools = request.copyWith(
      tools: openRouterTools,
      stream: true,
    );

    while (iteration < maxIterations) {
      iteration++;

      yield ToolChainStreamEventExtension.iterationStart(iteration);

      // Track streaming state for tool calls
      String? currentContent = '';
      List<ToolCallDelta>? pendingToolCalls;

      // Make streaming API request
      await for (final chunk in _apiClient.createChatCompletionStream(
        requestWithTools.copyWith(messages: conversationHistory),
      )) {
        final choice = chunk.choices.first;
        final delta = choice.delta;

        // Accumulate content
        if (delta.content != null) {
          currentContent = (currentContent ?? '') + delta.content!;
          yield ToolChainStreamEventExtension.content(delta.content!);
        }

        // Handle tool calls
        if (delta.toolCalls != null) {
          pendingToolCalls = delta.toolCalls;
          yield ToolChainStreamEventExtension.toolCallStart(
            delta.toolCalls!
                .map(
                  (delta) => ToolCall(
                    id: delta.id ?? '',
                    type: delta.type ?? 'function',
                    function: ToolFunction(
                      name: delta.function?.name ?? '',
                      arguments: delta.function?.arguments ?? '{}',
                    ),
                  ),
                )
                .toList(),
          );
        }

        // Check for completion
        if (choice.finishReason != null) {
          if (choice.finishReason == 'tool_calls' && pendingToolCalls != null) {
            // Create assistant message with tool calls
            final assistantChatMessage = ChatMessage(
              role: 'assistant',
              content: currentContent ?? '',
              toolCalls: pendingToolCalls
                  .map(
                    (delta) => ToolCall(
                      id: delta.id ?? '',
                      type: delta.type ?? 'function',
                      function: ToolFunction(
                        name: delta.function?.name ?? '',
                        arguments: delta.function?.arguments ?? '{}',
                      ),
                    ),
                  )
                  .toList(),
            );

            conversationHistory.add(assistantChatMessage);
            yield ToolChainStreamEventExtension.toolExecutionStart(
              pendingToolCalls
                  .map(
                    (delta) => ToolCall(
                      id: delta.id ?? '',
                      type: delta.type ?? 'function',
                      function: ToolFunction(
                        name: delta.function?.name ?? '',
                        arguments: delta.function?.arguments ?? '{}',
                      ),
                    ),
                  )
                  .toList(),
            );

            // Execute tools
            for (final toolCallDelta in pendingToolCalls) {
              final toolCall = ToolCall(
                id: toolCallDelta.id ?? '',
                type: toolCallDelta.type ?? 'function',
                function: ToolFunction(
                  name: toolCallDelta.function?.name ?? '',
                  arguments: toolCallDelta.function?.arguments ?? '{}',
                ),
              );
              final step = await _executeToolCall(toolCall);
              executionSteps.add(step);

              yield ToolChainStreamEventExtension.toolExecuted(step);

              // Add tool result to conversation
              final toolResultChatMessage = ChatMessage(
                role: 'tool',
                content: step.result.success
                    ? (step.result.content ?? '')
                    : 'Error: ${step.result.error}',
                toolCallId: toolCall.id,
                name: toolCall.function.name,
              );

              conversationHistory.add(toolResultChatMessage);
            }

            // Continue to next iteration
            break;
          } else {
            // Final response without tool calls
            final finalChatMessage = ChatMessage(
              role: 'assistant',
              content: currentContent ?? '',
            );

            conversationHistory.add(finalChatMessage);

            yield ToolChainStreamEventExtension.completed(
              ToolChainResult(
                finalResponse: ChatCompletionResponse(
                  choices: [
                    ChatCompletionChoice(
                      message: finalChatMessage,
                      finishReason: choice.finishReason,
                      index: 0,
                    ),
                  ],
                  id: '', // Will be filled by actual response
                  model: request.model,
                  object: 'chat.completion',
                  created: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  usage: Usage(
                    promptTokens: 0,
                    totalTokens: 0,
                  ), // Add required usage parameter
                ),
                conversationHistory: conversationHistory,
                executionSteps: executionSteps,
                iterations: iteration,
                completed: true,
              ),
            );
            return;
          }
        }
      }
    }

    // Reached maximum iterations
    yield ToolChainStreamEventExtension.error(
      ToolChainException(
        'Tool chain exceeded maximum iterations ($maxIterations)',
        conversationHistory: conversationHistory,
        executionSteps: executionSteps,
      ),
    );
  }

  /// Execute a single tool call
  Future<ToolExecutionStep> _executeToolCall(ToolCall toolCall) async {
    final startTime = DateTime.now();

    try {
      final arguments = toolCall.function.arguments.isNotEmpty
          ? jsonDecode(toolCall.function.arguments) as Map<String, dynamic>
          : <String, dynamic>{};

      final result = await _toolRegistry.executeTool(
        toolName: toolCall.function.name,
        arguments: arguments,
      );

      return ToolExecutionStep(
        toolCall: toolCall,
        arguments: arguments,
        result: ToolExecutionResult.success(
          result.content != null
              ? result.content!.map((c) => c.text).join('\n')
              : '',
        ),
        executionTime: DateTime.now().difference(startTime),
      );
    } catch (e) {
      return ToolExecutionStep(
        toolCall: toolCall,
        arguments: {},
        result: ToolExecutionResult.error(e.toString()),
        executionTime: DateTime.now().difference(startTime),
      );
    }
  }
}

/// Result of executing a tool calling chain
class ToolChainResult {
  final ChatCompletionResponse finalResponse;
  final List<ChatMessage> conversationHistory;
  final List<ToolExecutionStep> executionSteps;
  final int iterations;
  final bool completed;

  const ToolChainResult({
    required this.finalResponse,
    required this.conversationHistory,
    required this.executionSteps,
    required this.iterations,
    required this.completed,
  });
}

/// A single step in tool execution
class ToolExecutionStep {
  final ToolCall toolCall;
  final Map<String, dynamic> arguments;
  final ToolExecutionResult result;
  final Duration executionTime;

  const ToolExecutionStep({
    required this.toolCall,
    required this.arguments,
    required this.result,
    required this.executionTime,
  });
}

/// Result of executing a tool
class ToolExecutionResult {
  final bool success;
  final String? content;
  final String? error;

  const ToolExecutionResult._({
    required this.success,
    this.content,
    this.error,
  });

  factory ToolExecutionResult.success(String content) {
    return ToolExecutionResult._(success: true, content: content);
  }

  factory ToolExecutionResult.error(String error) {
    return ToolExecutionResult._(success: false, error: error);
  }
}

/// Events emitted during streaming tool chain execution
abstract class ToolChainStreamEvent {}

class ToolChainIterationStartEvent extends ToolChainStreamEvent {
  final int iteration;
  ToolChainIterationStartEvent(this.iteration);
}

class ToolChainContentEvent extends ToolChainStreamEvent {
  final String content;
  ToolChainContentEvent(this.content);
}

class ToolChainToolCallStartEvent extends ToolChainStreamEvent {
  final List<ToolCall> toolCalls;
  ToolChainToolCallStartEvent(this.toolCalls);
}

class ToolChainToolExecutionStartEvent extends ToolChainStreamEvent {
  final List<ToolCall> toolCalls;
  ToolChainToolExecutionStartEvent(this.toolCalls);
}

class ToolChainToolExecutedEvent extends ToolChainStreamEvent {
  final ToolExecutionStep step;
  ToolChainToolExecutedEvent(this.step);
}

class ToolChainCompletedEvent extends ToolChainStreamEvent {
  final ToolChainResult result;
  ToolChainCompletedEvent(this.result);
}

class ToolChainErrorEvent extends ToolChainStreamEvent {
  final ToolChainException error;
  ToolChainErrorEvent(this.error);
}

extension ToolChainStreamEventExtension on ToolChainStreamEvent {
  static ToolChainIterationStartEvent iterationStart(int iteration) =>
      ToolChainIterationStartEvent(iteration);

  static ToolChainContentEvent content(String content) =>
      ToolChainContentEvent(content);

  static ToolChainToolCallStartEvent toolCallStart(List<ToolCall> toolCalls) =>
      ToolChainToolCallStartEvent(toolCalls);

  static ToolChainToolExecutionStartEvent toolExecutionStart(
    List<ToolCall> toolCalls,
  ) => ToolChainToolExecutionStartEvent(toolCalls);

  static ToolChainToolExecutedEvent toolExecuted(ToolExecutionStep step) =>
      ToolChainToolExecutedEvent(step);

  static ToolChainCompletedEvent completed(ToolChainResult result) =>
      ToolChainCompletedEvent(result);

  static ToolChainErrorEvent error(ToolChainException error) =>
      ToolChainErrorEvent(error);
}

/// Exception thrown during tool chain execution
class ToolChainException implements Exception {
  final String message;
  final List<ChatMessage> conversationHistory;
  final List<ToolExecutionStep> executionSteps;

  const ToolChainException(
    this.message, {
    required this.conversationHistory,
    required this.executionSteps,
  });

  @override
  String toString() => 'ToolChainException: $message';
}
